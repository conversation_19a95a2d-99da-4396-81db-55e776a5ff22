{"name": "digital-twin-server", "version": "1.0.0", "description": "Digital Twin Platform Backend Server", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@apollo/server": "^4.9.5", "@azure/ai-anomaly-detector": "^3.0.0-beta.5", "@azure/ai-text-analytics": "^5.1.0", "@azure/cosmos": "^4.0.0", "@azure/identity": "^4.0.1", "@azure/monitor-query": "^1.2.0", "@azure/service-bus": "^7.9.4", "@azure/storage-blob": "^12.17.0", "apollo-server-express": "^3.12.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "graphql": "^16.8.1", "graphql-scalars": "^1.22.4", "graphql-subscriptions": "^3.0.0", "graphql-upload": "^16.0.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "rate-limiter-flexible": "^4.0.1", "redis": "^4.6.10", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/graphql-upload": "^17.0.0", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.8.10", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "keywords": ["digital-twin", "azure", "graphql", "nodejs", "typescript"], "author": "Digital Twin Platform", "license": "MIT"}