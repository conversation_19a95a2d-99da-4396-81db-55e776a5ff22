# Server Configuration
PORT=4000
NODE_ENV=development

# Azure Configuration
AZURE_STORAGE_CONNECTION_STRING=your_azure_storage_connection_string
AZURE_STORAGE_CONTAINER_NAME=digital-twin-models
AZURE_COSMOS_ENDPOINT=your_cosmos_db_endpoint
AZURE_COSMOS_KEY=your_cosmos_db_key
AZURE_COSMOS_DATABASE_NAME=DigitalTwinDB
AZURE_SERVICE_BUS_CONNECTION_STRING=your_service_bus_connection_string

# Azure AI Services
AZURE_ANOMALY_DETECTOR_ENDPOINT=your_anomaly_detector_endpoint
AZURE_ANOMALY_DETECTOR_KEY=your_anomaly_detector_key
AZURE_TEXT_ANALYTICS_ENDPOINT=your_text_analytics_endpoint
AZURE_TEXT_ANALYTICS_KEY=your_text_analytics_key

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=104857600
ALLOWED_FILE_TYPES=.gltf,.glb,.bim,.ifc,.rvt

# Monitoring
AZURE_APPLICATION_INSIGHTS_CONNECTION_STRING=your_app_insights_connection_string